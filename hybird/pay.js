console.log('pay')
class Pay {
  constructor() {
    console.log('pay初始化')
  }
  async pay(product_id) {
    console.log('pay', product_id)
    const res = await fetch('https://61.244.24.37:8443/order/createOrderId?productId=' + product_id, {
      method: 'get',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then((res) => {
      return res.json()
    })
    console.log('pay', res)
    if (res.data.code === 200) {
      return new Promise((resolve, reject) => {
        if (window.flutter_inappwebview) {
          window.flutterObj.googlePay(res.data.googleProductId, res.data.orderId).then((res) => {
            resolve(res)
          }).catch((err) => {
            reject(err)
          })
        } else {
          resolve(true)
        }
      })
    }
  }
}

window.payObj = new Pay()